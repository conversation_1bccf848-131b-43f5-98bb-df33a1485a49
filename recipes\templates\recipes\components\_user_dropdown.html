<!-- User Dropdown Component -->
<div class="user-dropdown-container" x-data="userDropdown()" @click.away="closeDropdown()">
    <!-- User Icon Button -->
    <button class="user-icon-btn" @click="toggleDropdown()" :class="{ 'active': isOpen }" title="User Menu">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
            <circle cx="12" cy="7" r="4"/>
        </svg>
        <div class="user-dropdown-indicator" :class="{ 'rotated': isOpen }">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polyline points="6,9 12,15 18,9"/>
            </svg>
        </div>
    </button>

    <!-- Dropdown Content -->
    <div class="user-dropdown-content" x-ref="dropdown" x-show="isOpen" x-transition:enter="dropdown-enter" x-transition:enter-start="dropdown-enter-start" x-transition:enter-end="dropdown-enter-end" x-transition:leave="dropdown-leave" x-transition:leave-start="dropdown-leave-start" x-transition:leave-end="dropdown-leave-end">
        
        <!-- User Stats Section -->
        <div class="user-stats-section">
            <h4 class="user-section-title">Your Activity</h4>
            <div class="user-stats-grid">
                <div class="user-stat-card">
                    <div class="user-stat-icon">🔍</div>
                    <div class="user-stat-content">
                        <div class="user-stat-number" id="user-total-searches">0</div>
                        <div class="user-stat-label">Total Searches</div>
                    </div>
                </div>
                <div class="user-stat-card">
                    <div class="user-stat-icon">📅</div>
                    <div class="user-stat-content">
                        <div class="user-stat-number" id="user-week-searches">0</div>
                        <div class="user-stat-label">This Week</div>
                    </div>
                </div>
                <div class="user-stat-card">
                    <div class="user-stat-icon">⭐</div>
                    <div class="user-stat-content">
                        <div class="user-stat-number" id="user-popular-type">-</div>
                        <div class="user-stat-label">Most Used</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- User Actions Section -->
        <div class="user-actions-section">
            <h4 class="user-section-title">Quick Access</h4>
            <div class="user-actions-grid">
                <a href="{% url 'recipes:search_history' %}" class="user-action-item">
                    <div class="user-action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
                            <path d="M3 3v5h5"/>
                            <polyline points="12,7 12,12 16,16"/>
                        </svg>
                    </div>
                    <div class="user-action-content">
                        <div class="user-action-title">Search History</div>
                        <div class="user-action-subtitle">View all searches</div>
                    </div>
                </a>
                
                <a href="#" class="user-action-item" onclick="showComingSoon('Favourites')">
                    <div class="user-action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
                        </svg>
                    </div>
                    <div class="user-action-content">
                        <div class="user-action-title">Favourites</div>
                        <div class="user-action-subtitle">Saved recipes</div>
                    </div>
                </a>
                
                <a href="#" class="user-action-item" onclick="showComingSoon('Bookmarks')">
                    <div class="user-action-icon">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z"/>
                        </svg>
                    </div>
                    <div class="user-action-content">
                        <div class="user-action-title">Bookmarks</div>
                        <div class="user-action-subtitle">Quick access</div>
                    </div>
                </a>
            </div>
        </div>

        <!-- Future User Profile Section (placeholder) -->
        <div class="user-profile-section" style="display: none;">
            <h4 class="user-section-title">Profile</h4>
            <div class="user-profile-placeholder">
                <p>User profile features will be added here</p>
            </div>
        </div>
    </div>
</div>

<style>
/* User Dropdown Styles */
.user-dropdown-container {
    position: relative;
    display: inline-block;
}

.user-icon-btn {
    display: flex;
    align-items: center;
    gap: 6px;
    padding: 8px 12px;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #6b7280;
    font-size: 14px;
    font-weight: 500;
}

.user-icon-btn:hover {
    border-color: #FF6B35;
    color: #FF6B35;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.15);
}

.user-icon-btn.active {
    border-color: #FF6B35;
    color: #FF6B35;
    background: #fff3f0;
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.2);
}

.user-dropdown-indicator {
    transition: transform 0.3s ease;
}

.user-dropdown-indicator.rotated {
    transform: rotate(180deg);
}

.user-dropdown-content {
    position: absolute;
    top: calc(100% + 8px);
    right: 0;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    min-width: 320px;
    max-width: 400px;
    overflow: hidden;
}

/* Mobile Bottom Drawer Styles */
@media (max-width: 768px) {
    .user-dropdown-content {
        position: fixed;
        top: auto;
        bottom: 0;
        left: 0;
        right: 0;
        width: 100%;
        min-width: 100%;
        max-width: 100%;
        border-radius: 20px 20px 0 0;
        border: none;
        border-top: 2px solid #e5e7eb;
        box-shadow: 0 -10px 40px rgba(0, 0, 0, 0.2);
        max-height: 80vh;
        overflow-y: auto;
        transform: translateY(100%);
        transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    }

    .user-dropdown-content.mobile-drawer-open {
        transform: translateY(0);
    }

    /* Add a handle bar at the top */
    .user-dropdown-content::before {
        content: '';
        position: absolute;
        top: 12px;
        left: 50%;
        transform: translateX(-50%);
        width: 40px;
        height: 4px;
        background: #d1d5db;
        border-radius: 2px;
        z-index: 1;
    }

    /* Add padding for the handle */
    .user-stats-section {
        padding-top: 32px;
    }
}

/* Dropdown Animations */
.dropdown-enter {
    transition: all 0.3s ease;
}

.dropdown-enter-start {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
}

.dropdown-enter-end {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.dropdown-leave {
    transition: all 0.2s ease;
}

.dropdown-leave-start {
    opacity: 1;
    transform: translateY(0) scale(1);
}

.dropdown-leave-end {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
}

/* User Stats Section */
.user-stats-section {
    padding: 20px;
    border-bottom: 1px solid #f3f4f6;
    background: linear-gradient(135deg, #fff3f0, #ffffff);
}

.user-section-title {
    color: #FF6B35;
    font-size: 16px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.user-stats-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 12px;
}

.user-stat-card {
    background: white;
    border-radius: 12px;
    padding: 12px;
    text-align: center;
    border: 1px solid #ffe8e1;
    transition: all 0.2s ease;
}

.user-stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 107, 53, 0.1);
}

.user-stat-icon {
    font-size: 1.2rem;
    margin-bottom: 6px;
}

.user-stat-number {
    font-size: 1.2rem;
    font-weight: 700;
    color: #FF6B35;
    line-height: 1;
    margin-bottom: 2px;
}

.user-stat-label {
    color: #6b7280;
    font-size: 0.75rem;
    font-weight: 500;
}

/* User Actions Section */
.user-actions-section {
    padding: 20px;
}

.user-actions-grid {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.user-action-item {
    display: flex;
    align-items: center;
    gap: 12px;
    padding: 12px;
    border-radius: 12px;
    text-decoration: none;
    color: inherit;
    transition: all 0.2s ease;
    border: 1px solid transparent;
}

.user-action-item:hover {
    background: #fff3f0;
    border-color: #ffe8e1;
    color: inherit;
    text-decoration: none;
}

.user-action-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 36px;
    height: 36px;
    background: #f9fafb;
    border-radius: 8px;
    color: #6b7280;
    transition: all 0.2s ease;
}

.user-action-item:hover .user-action-icon {
    background: #FF6B35;
    color: white;
}

.user-action-content {
    flex: 1;
}

.user-action-title {
    font-weight: 600;
    color: #374151;
    font-size: 14px;
    margin-bottom: 2px;
}

.user-action-subtitle {
    color: #6b7280;
    font-size: 12px;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .user-dropdown-content {
        right: -10px;
        min-width: 280px;
        max-width: calc(100vw - 40px);
    }
    
    .user-stats-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .user-stat-card {
        display: flex;
        align-items: center;
        gap: 12px;
        text-align: left;
        padding: 10px 12px;
    }
    
    .user-stat-icon {
        font-size: 1.5rem;
        margin-bottom: 0;
    }
    
    .user-stat-number {
        font-size: 1.1rem;
    }
    
    .user-stat-label {
        font-size: 0.7rem;
    }
}
</style>

<script>
function userDropdown() {
    return {
        isOpen: false,
        
        init() {
            this.loadUserStats();
        },
        
        toggleDropdown() {
            this.isOpen = !this.isOpen;
            if (this.isOpen) {
                this.loadUserStats();
                this.handleMobileDrawer();
            } else {
                this.closeMobileDrawer();
            }
        },

        closeDropdown() {
            this.isOpen = false;
            this.closeMobileDrawer();
        },

        handleMobileDrawer() {
            // Check if we're on mobile
            if (window.innerWidth <= 768) {
                const dropdown = this.$refs.dropdown;
                if (dropdown) {
                    // Add mobile drawer class
                    dropdown.classList.add('mobile-drawer-open');
                    // Prevent body scroll
                    document.body.style.overflow = 'hidden';
                }
            }
        },

        closeMobileDrawer() {
            const dropdown = this.$refs.dropdown;
            if (dropdown) {
                dropdown.classList.remove('mobile-drawer-open');
                // Restore body scroll
                document.body.style.overflow = '';
            }
        },
        
        loadUserStats() {
            if (window.searchHistoryManager) {
                const stats = window.searchHistoryManager.getSearchStats();
                
                document.getElementById('user-total-searches').textContent = stats.totalSearches;
                document.getElementById('user-week-searches').textContent = stats.recentActivity.length;
                
                // Find most popular search type
                const searchTypes = stats.searchTypes;
                const mostPopular = Object.keys(searchTypes).reduce((a, b) => 
                    searchTypes[a] > searchTypes[b] ? a : b, 'ingredients'
                );
                document.getElementById('user-popular-type').textContent = 
                    stats.totalSearches > 0 ? mostPopular : '-';
            }
        }
    }
}

function showComingSoon(feature) {
    alert(`${feature} feature coming soon! This will allow you to save and organize your favorite recipes.`);
}
</script>
